import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON><PERSON>er,
  Modal<PERSON>eader,
  ModalOverlay,
  Select,
  Text,
  Textarea,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Image from 'next/image';
import React from 'react';

interface AddMemberListProps {
  isOpen: boolean;
  onClose: () => void;
  newMember: {
    status: string;
    group: string;
    remainingTime: number;
    totalTime: number;
    chargeEnabled: boolean;
    contact: string;
  };
  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  handleAddMember: () => void;
  nextStep: () => void;
  steps: number;
  setSteps: React.Dispatch<React.SetStateAction<number>>;
}

function AddMemberList({
  isOpen,
  onClose,
  newMember,
  handleInputChange,
  handleAddMember,
  nextStep,
  steps,
  setSteps,
}: AddMemberListProps) {
  const { t } = useTranslation('admin');
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      {steps === 0 && (
        <ModalContent>
          <ModalHeader fontSize={'sm'} textAlign={'center'}>
            {t('membersList.addMember.title')}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <FormControl my={4}>
              <FormLabel fontSize={'xs'} fontWeight={'light'} variant={'required'}>
                {t('membersList.addMember.form.email')}
              </FormLabel>
              <Input
                fontSize={'xs'}
                fontWeight={'light'}
                name="group"
                value={newMember.group}
                onChange={handleInputChange}
                placeholder={t('membersList.addMember.form.emailPlaceholder')}
              />
            </FormControl>

            <FormControl>
              <FormLabel fontSize={'xs'} fontWeight={'light'}>
                {t('membersList.addMember.form.groupSelect')}
              </FormLabel>
              <Select
                fontSize={'xs'}
                fontWeight={'light'}
                name="status"
                value={newMember.status}
                onChange={handleInputChange}
              >
                <option value="">{t('membersList.addMember.form.groupSelectPlaceholder')}</option>
                <option value="alpha">Alpha</option>
                <option value="group">Group</option>
                <option value="proto">Proto</option>
              </Select>
            </FormControl>

            <FormControl mt={4}>
              <FormLabel fontSize={'xs'} fontWeight={'light'}>
                {t('membersList.addMember.form.notes')}
              </FormLabel>
              <Textarea
                fontSize={'xs'}
                fontWeight={'light'}
                name="remainingTime"
                value={newMember.remainingTime.toString()}
                onChange={handleInputChange}
                placeholder={t('membersList.addMember.form.notesPlaceholder')}
              />
            </FormControl>
          </ModalBody>

          <ModalFooter justifyContent={'center'}>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              onClick={nextStep}
            >
              {t('membersList.addMember.buttons.add')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}

      {steps === 1 && (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            <Image src="/imgs/icons/mail.svg" alt="Vercel Logo" width={25} height={25} />
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('membersList.addMember.confirmation.title')}
            </Text>
            <Text>{t('membersList.addMember.confirmation.message1')}</Text>
            <Text>{t('membersList.addMember.confirmation.message2')}</Text>
          </ModalBody>

          <ModalFooter gap={4} justifyContent={'center'}>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="rounded"
              onClick={() => {
                setSteps(steps - 1);
              }}
            >
              {t('membersList.addMember.buttons.back')}
            </Button>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              onClick={() => {
                handleAddMember();
                nextStep();
              }}
            >
              {t('membersList.addMember.buttons.send')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}

      {steps === 2 && (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            <Image src="/imgs/icons/mail.svg" alt="Vercel Logo" width={25} height={25} />
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('membersList.addMember.success.message')}
            </Text>
          </ModalBody>

          <ModalFooter gap={4} justifyContent={'center'}>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              onClick={() => {
                setSteps(0);
                onClose();
              }}
            >
              {t('membersList.addMember.buttons.close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}
    </Modal>
  );
}

export default AddMemberList;
