import {
  Button,
  FormControl,
  FormLabel,
  Input,
  Modal,
  ModalBody,
  ModalClose<PERSON>utton,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  Select,
  VStack,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  filters: {
    keyword: string;
    status: string;
    groupType: string;
  };
  onFilterChange: (filters: { keyword: string; status: string; groupType: string }) => void;
  onApplyFilters: () => void;
}

export default function FilterModal({
  isOpen,
  onClose,
  filters,
  onFilterChange,
  onApplyFilters,
}: FilterModalProps) {
  const { t } = useTranslation('admin');

  const handleInputChange = (field: string, value: string) => {
    onFilterChange({
      ...filters,
      [field]: value,
    });
  };

  const handleApply = () => {
    onApplyFilters();
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader fontSize="lg" textAlign="center">
          {t('membersList.filter.title')}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4}>
            <FormControl>
              <FormLabel fontSize="sm" fontWeight="normal">
                {t('membersList.filter.keyword')}
              </FormLabel>
              <Input
                placeholder={t('membersList.filter.keywordPlaceholder')}
                value={filters.keyword}
                onChange={e => handleInputChange('keyword', e.target.value)}
                variant="outline"
                borderColor="gray.300"
              />
            </FormControl>

            <FormControl>
              <FormLabel fontSize="sm" fontWeight="normal">
                {t('membersList.filter.status')}
              </FormLabel>
              <Select
                placeholder={t('membersList.filter.statusPlaceholder')}
                value={filters.status}
                onChange={e => handleInputChange('status', e.target.value)}
                variant="outline"
                borderColor="gray.300"
              >
                <option value="pending">{t('membersList.filter.statusOptions.pending')}</option>
                <option value="active">{t('membersList.filter.statusOptions.active')}</option>
                <option value="deleted">{t('membersList.filter.statusOptions.deleted')}</option>
              </Select>
            </FormControl>

            <FormControl>
              <FormLabel fontSize="sm" fontWeight="normal">
                {t('membersList.filter.groupType')}
              </FormLabel>
              <Select
                placeholder={t('membersList.filter.groupTypePlaceholder')}
                value={filters.groupType}
                onChange={e => handleInputChange('groupType', e.target.value)}
                variant="outline"
                borderColor="gray.300"
              >
                <option value="グループA">グループA</option>
                <option value="グループB">グループB</option>
                <option value="グループC">グループC</option>
                <option value="グループD">グループD</option>
                <option value="グループE">グループE</option>
                <option value="グループF">グループF</option>
              </Select>
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter justifyContent="center">
          <Button variant="roundedBlue" px="3rem" onClick={handleApply}>
            {t('membersList.filter.applyButton')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
